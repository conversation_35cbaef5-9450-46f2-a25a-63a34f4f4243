import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';

import '../models/wallet_state.dart';
import '../models/transaction_model.dart';
import '../repositories/payment_repository.dart';
import '../services/transaction_manager.dart';

class WalletManager extends ChangeNotifier {
  static final WalletManager _instance = WalletManager._internal();
  factory WalletManager() => _instance;
  WalletManager._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final PaymentRepository _paymentRepository = PaymentRepository();
  final TransactionManager _transactionManager = TransactionManager();

  WalletState _state = WalletState.initial();
  StreamSubscription<DocumentSnapshot>? _balanceSubscription;
  StreamSubscription<QuerySnapshot>? _transactionsSubscription;
  DocumentReference<Map<String, dynamic>>? _walletDoc;

  bool _isInitialized = false;
  Completer<void>? _initializationCompleter;

  WalletState get state => _state;
  double get balance => _state.balance;
  bool get isInitialized => _isInitialized;
  bool get isLoading => _state.isLoading;

  Future<void> initialize() async {
    if (_isInitialized) return;

    if (_initializationCompleter != null) {
      return _initializationCompleter!.future;
    }

    _initializationCompleter = Completer<void>();

    try {
      _updateState(
        _state.copyWith(status: WalletStatus.initializing, isLoading: true),
      );

      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      await _paymentRepository.initialize();
      await _transactionManager.initialize(user.uid);

      _walletDoc = _firestore.collection('wallets').doc(user.uid);

      // Initialize wallet document if it doesn't exist
      await _initializeWalletDocument();

      // Set up real-time listeners
      await _setupListeners();

      _isInitialized = true;
      _updateState(
        _state.copyWith(status: WalletStatus.ready, isLoading: false),
      );

      _initializationCompleter!.complete();
    } catch (e) {
      _updateState(WalletState.error('Failed to initialize wallet: $e'));
      _initializationCompleter!.completeError(e);
      rethrow;
    }
  }

  Future<void> _initializeWalletDocument() async {
    if (_walletDoc == null) return;

    final docSnapshot = await _walletDoc!.get();
    if (!docSnapshot.exists) {
      // Create new wallet document
      await _walletDoc!.set({
        'balance': 0.0,
        'createdAt': FieldValue.serverTimestamp(),
        'lastUpdated': FieldValue.serverTimestamp(),
      });
    } else {
      // Ensure balance field exists
      final data = docSnapshot.data();
      if (data == null || !data.containsKey('balance')) {
        await _walletDoc!.update({
          'balance': 0.0,
          'lastUpdated': FieldValue.serverTimestamp(),
        });
      }
    }
  }

  Future<void> _setupListeners() async {
    if (_walletDoc == null) return;

    // Listen to balance changes
    _balanceSubscription = _walletDoc!.snapshots().listen(
      (snapshot) {
        if (snapshot.exists) {
          final data = snapshot.data();
          final balance = (data?['balance'] ?? 0.0).toDouble();
          _updateState(
            _state.copyWith(balance: balance, lastUpdated: DateTime.now()),
          );
        }
      },
      onError: (error) {
        debugPrint('Error listening to wallet balance: $error');
        _updateState(
          _state.copyWith(
            status: WalletStatus.error,
            errorMessage: 'Failed to sync wallet balance: $error',
          ),
        );
      },
    );

    // Listen to transaction changes
    _transactionsSubscription = _walletDoc!
        .collection('transactions')
        .orderBy('timestamp', descending: true)
        .limit(100)
        .snapshots()
        .listen(
          (snapshot) {
            final transactions =
                snapshot.docs
                    .map((doc) => TransactionModel.fromFirestore(doc))
                    .toList();

            final totalEarnings = _calculateTotalEarnings(transactions);
            final totalSpent = _calculateTotalSpent(transactions);

            _updateState(
              _state.copyWith(
                transactions: transactions,
                totalEarnings: totalEarnings,
                totalSpent: totalSpent,
                lastUpdated: DateTime.now(),
              ),
            );
          },
          onError: (error) {
            debugPrint('Error listening to transactions: $error');
          },
        );
  }

  double _calculateTotalEarnings(List<TransactionModel> transactions) {
    return transactions
        .where((t) => t.isCredit && t.isCompleted)
        .fold(0.0, (total, t) => total + t.amount);
  }

  double _calculateTotalSpent(List<TransactionModel> transactions) {
    return transactions
        .where((t) => t.isDebit && t.isCompleted)
        .fold(0.0, (total, t) => total + t.amount);
  }

  void _updateState(WalletState newState) {
    if (_state != newState) {
      _state = newState;
      notifyListeners();
    }
  }

  Future<bool> addFunds(double amount, {String? paymentMethodId}) async {
    if (!_isInitialized) {
      throw StateError('WalletManager not initialized');
    }

    if (amount <= 0) {
      throw ArgumentError('Amount must be positive');
    }

    _updateState(_state.setLoading(true));

    try {
      // Process payment
      final paymentResult = await _paymentRepository.processPayment(
        amount: amount,
        currency: 'USD',
        paymentMethodId: paymentMethodId,
      );

      if (!paymentResult.isSuccess) {
        _updateState(_state.setLoading(false));
        return false;
      }

      // Create transaction record
      final transaction = TransactionModel(
        id: '', // Will be set by Firestore
        type: TransactionType.credit,
        amount: amount,
        description: 'Added funds',
        timestamp: DateTime.now(),
        status: TransactionStatus.completed,
        paymentMethodId: paymentMethodId,
        externalTransactionId: paymentResult.transactionId,
      );

      // Add transaction and update balance atomically
      await _transactionManager.addTransaction(
        transaction,
        _state.balance + amount,
      );

      _updateState(_state.setLoading(false));
      return true;
    } catch (e) {
      _updateState(
        _state.copyWith(
          status: WalletStatus.error,
          errorMessage: 'Failed to add funds: $e',
          isLoading: false,
        ),
      );
      return false;
    }
  }

  Future<bool> deductFunds({
    required double amount,
    required String description,
    String? postId,
  }) async {
    if (!_isInitialized) {
      throw StateError('WalletManager not initialized');
    }

    if (amount <= 0) {
      throw ArgumentError('Amount must be positive');
    }

    if (_state.balance < amount) {
      return false; // Insufficient funds
    }

    _updateState(_state.setLoading(true));

    try {
      final transaction = TransactionModel(
        id: '', // Will be set by Firestore
        type: TransactionType.debit,
        amount: amount,
        description: description,
        timestamp: DateTime.now(),
        status: TransactionStatus.completed,
        postId: postId,
      );

      // Deduct from balance and add transaction atomically
      await _transactionManager.addTransaction(
        transaction,
        _state.balance - amount,
      );

      _updateState(_state.setLoading(false));
      return true;
    } catch (e) {
      _updateState(
        _state.copyWith(
          status: WalletStatus.error,
          errorMessage: 'Failed to deduct funds: $e',
          isLoading: false,
        ),
      );
      return false;
    }
  }

  String formatCurrency(double amount) {
    return NumberFormat.currency(symbol: '\$', decimalDigits: 2).format(amount);
  }

  void clearError() {
    _updateState(_state.clearError());
  }

  @override
  void dispose() {
    _balanceSubscription?.cancel();
    _transactionsSubscription?.cancel();
    super.dispose();
  }
}
