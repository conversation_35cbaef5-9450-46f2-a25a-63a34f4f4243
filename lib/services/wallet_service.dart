// ignore_for_file: avoid_print

import 'dart:async';
import 'package:intl/intl.dart';

import '../managers/wallet_manager.dart';
import '../models/transaction_model.dart';

// Legacy Transaction class for backward compatibility
class Transaction {
  final String id;
  final String type;
  final double amount;
  final String description;
  final DateTime timestamp;
  final String status;
  final String? postId;

  Transaction({
    required this.id,
    required this.type,
    required this.amount,
    required this.description,
    required this.timestamp,
    required this.status,
    this.postId,
  });

  factory Transaction.fromTransactionModel(TransactionModel model) {
    return Transaction(
      id: model.id,
      type: model.type.name,
      amount: model.amount,
      description: model.description,
      timestamp: model.timestamp,
      status: model.status.name,
      postId: model.postId,
    );
  }
}

/// Legacy WalletService that now delegates to WalletManager
/// This maintains backward compatibility while using the new architecture
class WalletService {
  static final WalletService _instance = WalletService._internal();
  factory WalletService() => _instance;
  WalletService._internal();

  final WalletManager _walletManager = WalletManager();

  double get currentBalance => _walletManager.balance;
  bool get isInitialized => _walletManager.isInitialized;

  /// Initialize the wallet service
  Future<void> initialize() async {
    await _walletManager.initialize();
  }

  Future<void> dispose() async {
    // WalletManager handles its own disposal
  }

  // ---------- Public API  ----------

  Future<bool> addFunds(double amount) async {
    return await _walletManager.addFunds(amount);
  }

  Future<bool> deductBalance(
    double amount,
    String description, {
    String? postId,
  }) async {
    return await _walletManager.deductFunds(
      amount: amount,
      description: description,
      postId: postId,
    );
  }

  Future<bool> withdrawFunds(double amount, String bankAccount) async {
    // TODO: Implement withdrawal through WalletManager
    // For now, return false as this feature needs to be implemented in the new architecture
    return false;
  }

  Future<List<Transaction>> getTransactionHistory({int limit = 50}) async {
    final transactions = _walletManager.state.transactions.take(limit).toList();
    return transactions
        .map((t) => Transaction.fromTransactionModel(t))
        .toList();
  }

  Future<double> getTotalEarnings() async {
    return _walletManager.state.totalEarnings;
  }

  Future<double> getTotalSpent() async {
    return _walletManager.state.totalSpent;
  }

  String formatCurrency(double amount) =>
      NumberFormat.currency(symbol: '\$', decimalDigits: 2).format(amount);

  // Dev utility
  Future<bool> simulatePayment(double amount) => addFunds(amount);
}
